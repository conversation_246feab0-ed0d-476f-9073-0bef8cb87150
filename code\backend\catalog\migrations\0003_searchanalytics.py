# Generated by Django 5.2.4 on 2025-08-08 21:16

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("catalog", "0002_add_service_constraints"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SearchAnalytics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "query",
                    models.CharField(help_text="Search query text", max_length=500),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True,
                        help_text="Session ID for anonymous users",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "filters",
                    models.JSONField(
                        blank=True, default=dict, help_text="Applied search filters"
                    ),
                ),
                (
                    "results_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of results returned"
                    ),
                ),
                (
                    "response_time_ms",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Search response time in milliseconds",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="When the search was performed",
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, help_text="IP address of the user", null=True
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True, help_text="User agent string", null=True
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who performed the search (null for anonymous)",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Search Analytics",
                "verbose_name_plural": "Search Analytics",
                "db_table": "catalog_search_analytics",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["query", "timestamp"],
                        name="catalog_sea_query_16d4e8_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="catalog_sea_user_id_23aa5b_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="catalog_sea_timesta_046083_idx"
                    ),
                    models.Index(
                        fields=["session_id"], name="catalog_sea_session_0d550d_idx"
                    ),
                ],
            },
        ),
    ]
