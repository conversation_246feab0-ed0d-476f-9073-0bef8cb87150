/**
 * Search API Service
 * 
 * Provides comprehensive search functionality including:
 * - Full-text search with filters
 * - Real-time suggestions
 * - Voice search integration
 * - Search analytics tracking
 * - Recommendation system
 */

import { apiClient } from './api/client';

export interface SearchFilters {
  categories?: string[];
  priceRange?: [number, number];
  rating?: number;
  distance?: number;
  availability?: boolean;
  sortBy?: 'relevance' | 'price' | 'rating' | 'distance';
  features?: string[];
}

export interface SearchParams extends SearchFilters {
  query: string;
  page?: number;
  limit?: number;
}

export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'service' | 'provider';
  category?: string;
  price?: number;
  rating?: number;
  image?: string;
  provider_name?: string;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
}

export interface SearchResponse {
  results: SearchResult[];
  total_count: number;
  has_next: boolean;
  has_previous: boolean;
  page: number;
  total_pages: number;
}

export interface SearchSuggestion {
  text: string;
  type: 'service' | 'provider' | 'category' | 'location' | 'query';
  count?: number;
}

export interface VoiceSearchRequest {
  audio_data: string; // Base64 encoded audio
  language_code?: string;
  format?: 'wav' | 'mp3' | 'ogg';
}

export interface VoiceSearchResponse {
  success: boolean;
  transcript?: string;
  confidence?: number;
  search_results?: SearchResult[];
  error?: string;
}

export interface SearchAnalyticsData {
  query: string;
  timestamp: number;
  filters: SearchFilters;
  results_count?: number;
  user_id?: string;
  session_id?: string;
}

export interface RecommendationParams {
  user_id?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  category?: string;
  limit?: number;
}

class SearchApiService {
  private baseUrl = '/api/v1';

  /**
   * Perform comprehensive search with filters
   */
  async search(params: SearchParams): Promise<SearchResponse> {
    try {
      const response = await apiClient.get<SearchResponse>(`${this.baseUrl}/search/`, {
        params: {
          q: params.query,
          categories: params.categories?.join(','),
          price_min: params.priceRange?.[0],
          price_max: params.priceRange?.[1],
          min_rating: params.rating,
          max_distance: params.distance,
          availability: params.availability,
          sort_by: params.sortBy,
          features: params.features?.join(','),
          page: params.page || 1,
          limit: params.limit || 20,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Search API error:', error);
      throw new Error('Search failed. Please try again.');
    }
  }

  /**
   * Get real-time search suggestions
   */
  async getSuggestions(query: string, limit: number = 8): Promise<SearchSuggestion[]> {
    try {
      if (query.length < 2) {
        return [];
      }

      const response = await apiClient.get<{ suggestions: SearchSuggestion[] }>(
        `${this.baseUrl}/search/suggestions/`,
        {
          params: {
            q: query,
            limit,
          },
        }
      );

      return response.data.suggestions || [];
    } catch (error) {
      console.error('Suggestions API error:', error);
      return [];
    }
  }

  /**
   * Process voice search audio
   */
  async processVoiceSearch(audioData: VoiceSearchRequest): Promise<VoiceSearchResponse> {
    try {
      const response = await apiClient.post<VoiceSearchResponse>(
        `${this.baseUrl}/search/voice/`,
        audioData,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Voice search API error:', error);
      return {
        success: false,
        error: 'Voice search failed. Please try again.',
      };
    }
  }

  /**
   * Get personalized recommendations
   */
  async getRecommendations(params: RecommendationParams = {}): Promise<SearchResult[]> {
    try {
      const response = await apiClient.get<{ recommendations: SearchResult[] }>(
        `${this.baseUrl}/search/recommendations/`,
        {
          params: {
            user_id: params.user_id,
            latitude: params.location?.latitude,
            longitude: params.location?.longitude,
            category: params.category,
            limit: params.limit || 10,
          },
        }
      );

      return response.data.recommendations || [];
    } catch (error) {
      console.error('Recommendations API error:', error);
      return [];
    }
  }

  /**
   * Track search analytics
   */
  async trackSearch(data: SearchAnalyticsData): Promise<void> {
    try {
      // Transform data to match backend API format
      const analyticsPayload = {
        query: data.query,
        filters: data.filters || {},
        results_count: data.results_count || 0,
        response_time_ms: data.timestamp ? Date.now() - data.timestamp : undefined,
        session_id: data.session_id,
      };

      await apiClient.post(`/api/catalog/search/analytics/`, analyticsPayload);
    } catch (error) {
      console.error('Search analytics tracking error:', error);
      // Don't throw error for analytics - it's not critical
    }
  }

  /**
   * Get popular search queries
   */
  async getPopularQueries(limit: number = 10): Promise<string[]> {
    try {
      const response = await apiClient.get<{ queries: Array<{ query: string; count: number }> }>(
        `/api/catalog/search/popular/`,
        {
          params: { limit },
        }
      );

      // Extract just the query strings from the response
      return response.data.queries?.map(item => item.query) || [];
    } catch (error) {
      console.error('Popular queries API error:', error);
      return [];
    }
  }

  /**
   * Get search analytics summary
   */
  async getSearchAnalyticsSummary(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<{
    total_searches: number;
    unique_queries: number;
    average_results_per_search: number;
    average_response_time: number;
    success_rate: number;
    popular_categories: Array<{ category: string; count: number }>;
  }> {
    try {
      const response = await apiClient.get(
        `/api/catalog/search/analytics/summary/`,
        {
          params: { timeframe },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Search analytics summary API error:', error);
      return {
        total_searches: 0,
        unique_queries: 0,
        average_results_per_search: 0,
        average_response_time: 0,
        success_rate: 0,
        popular_categories: [],
      };
    }
  }

  /**
   * Get search analytics summary
   */
  async getSearchAnalytics(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<{
    total_searches: number;
    unique_queries: number;
    popular_categories: Array<{ category: string; count: number }>;
    average_results_per_search: number;
    success_rate: number;
  }> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/search/analytics/summary/`,
        {
          params: { timeframe },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Search analytics API error:', error);
      return {
        total_searches: 0,
        unique_queries: 0,
        popular_categories: [],
        average_results_per_search: 0,
        success_rate: 0,
      };
    }
  }

  /**
   * Get category-specific search results
   */
  async searchByCategory(
    category: string,
    filters: Omit<SearchFilters, 'categories'> = {},
    page: number = 1,
    limit: number = 20
  ): Promise<SearchResponse> {
    return this.search({
      query: '',
      categories: [category],
      ...filters,
      page,
      limit,
    });
  }

  /**
   * Get location-based search results
   */
  async searchNearby(
    location: { latitude: number; longitude: number },
    query: string = '',
    radius: number = 25,
    filters: SearchFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<SearchResponse> {
    try {
      const response = await apiClient.get<SearchResponse>(`${this.baseUrl}/search/nearby/`, {
        params: {
          q: query,
          latitude: location.latitude,
          longitude: location.longitude,
          radius,
          categories: filters.categories?.join(','),
          price_min: filters.priceRange?.[0],
          price_max: filters.priceRange?.[1],
          min_rating: filters.rating,
          sort_by: filters.sortBy,
          page,
          limit,
        },
      });

      return response.data;
    } catch (error) {
      console.error('Nearby search API error:', error);
      throw new Error('Location-based search failed. Please try again.');
    }
  }

  /**
   * Save search to favorites
   */
  async saveSearchToFavorites(query: string, filters: SearchFilters): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/search/favorites/`, {
        query,
        filters,
      });
    } catch (error) {
      console.error('Save search to favorites error:', error);
      throw new Error('Failed to save search to favorites.');
    }
  }

  /**
   * Get saved favorite searches
   */
  async getFavoriteSearches(): Promise<Array<{
    id: string;
    query: string;
    filters: SearchFilters;
    created_at: string;
  }>> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/search/favorites/`);
      return response.data.favorites || [];
    } catch (error) {
      console.error('Get favorite searches error:', error);
      return [];
    }
  }

  /**
   * Delete favorite search
   */
  async deleteFavoriteSearch(favoriteId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/search/favorites/${favoriteId}/`);
    } catch (error) {
      console.error('Delete favorite search error:', error);
      throw new Error('Failed to delete favorite search.');
    }
  }

  /**
   * Get search history
   */
  async getSearchHistory(limit: number = 20): Promise<Array<{
    query: string;
    timestamp: number;
    filters: SearchFilters;
    results_count: number;
  }>> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/search/history/`, {
        params: { limit },
      });
      return response.data.history || [];
    } catch (error) {
      console.error('Get search history error:', error);
      return [];
    }
  }

  /**
   * Clear search history
   */
  async clearSearchHistory(): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/search/history/`);
    } catch (error) {
      console.error('Clear search history error:', error);
      throw new Error('Failed to clear search history.');
    }
  }

  /**
   * Report search result (for improving search quality)
   */
  async reportSearchResult(
    query: string,
    resultId: string,
    action: 'click' | 'irrelevant' | 'helpful'
  ): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/search/feedback/`, {
        query,
        result_id: resultId,
        action,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error('Report search result error:', error);
      // Don't throw error for feedback - it's not critical
    }
  }
}

export const searchApiService = new SearchApiService();
