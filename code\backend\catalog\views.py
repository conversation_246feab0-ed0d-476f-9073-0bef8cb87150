"""
Service Catalog API views for Vierla Beauty Services Marketplace
Enhanced Django REST Framework views with filtering and search
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from rest_framework.parsers import <PERSON>PartParser, JSONParser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Prefetch
from django.db.models import Q
from django.http import JsonResponse
import logging

from .models import ServiceCategory, ServiceProvider, Service, SearchAnalytics
from .serializers import (
    ServiceCategorySerializer, ServiceCategoryListSerializer,
    ServiceProviderSerializer, ServiceProviderListSerializer,
    ServiceSerializer, ServiceListSerializer, ServiceCreateUpdateSerializer,
    ServiceSearchSerializer
)
from .filters import ServiceFilter, ServiceProviderFilter, ServiceCategoryFilter


class ServiceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for ServiceCategory with read-only operations
    Supports hierarchical categories and filtering
    """

    queryset = ServiceCategory.objects.filter(is_active=True)
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceCategoryFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceCategoryListSerializer
        return ServiceCategorySerializer

    def get_queryset(self):
        """Optimize queryset with prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'retrieve':
            # Include subcategories for detail view
            queryset = queryset.prefetch_related(
                Prefetch(
                    'subcategories',
                    queryset=ServiceCategory.objects.filter(is_active=True)
                )
            )

        return queryset

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services for a specific category"""
        category = self.get_object()
        services = Service.objects.filter(
            category=category,
            is_active=True
        ).select_related('provider', 'category')

        # Apply service filtering
        service_filter = ServiceFilter(request.GET, queryset=services)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)


class ServiceProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for ServiceProvider with read-only operations
    Supports filtering by location, rating, and categories
    """

    queryset = ServiceProvider.objects.filter(is_active=True)
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceProviderFilter
    search_fields = ['business_name', 'business_description', 'city']
    ordering_fields = ['business_name', 'rating', 'created_at', 'total_bookings']
    ordering = ['-is_featured', '-rating', 'business_name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceProviderListSerializer
        return ServiceProviderSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset()

        queryset = queryset.select_related('user').prefetch_related('categories')

        return queryset

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services for a specific provider"""
        provider = self.get_object()
        services = Service.objects.filter(
            provider=provider,
            is_active=True
        ).select_related('category')

        # Apply service filtering
        service_filter = ServiceFilter(request.GET, queryset=services)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Service with full CRUD operations
    Supports comprehensive filtering, search, and sorting
    """

    queryset = Service.objects.filter(is_active=True)
    permission_classes = [AllowAny]  # TODO: Implement proper permissions
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceFilter
    search_fields = ['name', 'description', 'provider__business_name', 'category__name']
    ordering_fields = ['name', 'base_price', 'duration', 'created_at', 'booking_count']
    ordering = ['-is_popular', 'base_price', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ServiceCreateUpdateSerializer
        return ServiceSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'list':
            # Optimized for list view
            queryset = queryset.select_related('provider', 'category')
        else:
            # Full optimization for detail view
            queryset = queryset.select_related(
                'provider__user', 'category'
            ).prefetch_related('provider__categories')

        return queryset

    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        Enhanced search endpoint with comprehensive filtering
        """
        # Validate search parameters
        search_serializer = ServiceSearchSerializer(data=request.GET)
        if not search_serializer.is_valid():
            return Response(
                search_serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get base queryset
        queryset = self.get_queryset()

        # Apply filters
        service_filter = ServiceFilter(request.GET, queryset=queryset)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data

            # Add search metadata
            response_data['search_metadata'] = {
                'total_results': filtered_services.count(),
                'filters_applied': {
                    key: value for key, value in request.GET.items()
                    if value and key != 'page'
                }
            }

            return Response(response_data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response({
            'results': serializer.data,
            'search_metadata': {
                'total_results': len(serializer.data),
                'filters_applied': {
                    key: value for key, value in request.GET.items()
                    if value
                }
            }
        })

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular services"""
        popular_services = self.get_queryset().filter(is_popular=True)

        # Apply additional filtering if provided
        service_filter = ServiceFilter(request.GET, queryset=popular_services)
        filtered_services = service_filter.qs

        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get services from featured providers"""
        featured_services = self.get_queryset().filter(provider__is_featured=True)

        # Apply additional filtering if provided
        service_filter = ServiceFilter(request.GET, queryset=featured_services)
        filtered_services = service_filter.qs

        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)


class EnhancedSearchView(APIView):
    """
    Enhanced search endpoint with real-time search, suggestions, and search history
    Optimized for mobile with debouncing support
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Enhanced search with multiple features:
        - Real-time search results
        - Search suggestions
        - Advanced filtering
        - Performance optimized queries
        """
        query = request.GET.get('q', '').strip()
        search_type = request.GET.get('type', 'services')  # services, providers, suggestions
        limit = min(int(request.GET.get('limit', 10)), 50)  # Max 50 results

        # Handle different search types
        if search_type == 'suggestions':
            return self._get_search_suggestions(query, limit, request)
        elif search_type == 'providers':
            return self._search_providers(query, request, limit)
        else:  # Default to services
            return self._search_services(query, request, limit)

    def _get_search_suggestions(self, query, limit, request):
        """Get real-time search suggestions using SearchSuggestionEngine"""
        from .search_algorithms import SearchSuggestionEngine

        suggestion_engine = SearchSuggestionEngine()

        # Extract filters from request
        category_filter = request.GET.get('category')
        location_filter = request.GET.get('location')

        # Get suggestions
        suggestions = suggestion_engine.get_suggestions(
            query,
            limit=limit,
            category_filter=category_filter,
            location_filter=location_filter,
            return_strings=False  # API should return dict format
        )

        return Response({
            'suggestions': suggestions,
            'query': query,
            'count': len(suggestions)
        })

    def _search_services(self, query, request, limit):
        """Search services using advanced search algorithm"""
        from .search_algorithms import AdvancedSearchAlgorithm
        import time

        search_algorithm = AdvancedSearchAlgorithm()
        start_time = time.time()

        # Extract filters
        filters = self._extract_filters(request)

        # Perform search
        if query:
            search_results = search_algorithm.search(query, filters=filters)
        else:
            # Return popular services if no query
            search_results = Service.objects.filter(
                is_active=True,
                is_available=True,
                is_popular=True
            ).select_related('provider', 'category')[:limit]

            # Convert to expected format
            search_results = [
                {
                    'service': service,
                    'relevance_score': 1.0,
                    'final_score': 1.0
                }
                for service in search_results
            ]

        # Limit results
        search_results = search_results[:limit]

        # Serialize results
        services_data = []
        for result in search_results:
            service = result['service'] if isinstance(result, dict) else result
            from .serializers import ServiceListSerializer
            serializer = ServiceListSerializer(service, context={'request': request})
            service_data = serializer.data

            # Add search metadata
            if isinstance(result, dict):
                service_data['search_metadata'] = {
                    'relevance_score': result.get('relevance_score', 1.0),
                    'final_score': result.get('final_score', 1.0)
                }

            services_data.append(service_data)

        # Track search analytics
        if query:  # Only track actual searches, not popular service requests
            self._track_search_analytics(
                query=query,
                filters=filters,
                results_count=len(services_data),
                response_time_ms=(time.time() - start_time) * 1000,
                request=request
            )

        return Response({
            'results': services_data,
            'count': len(services_data),
            'query': query,
            'type': 'services',
            'filters_applied': filters
        })

    def _search_providers(self, query, request, limit):
        """Search providers with basic filtering"""
        import time
        start_time = time.time()

        queryset = ServiceProvider.objects.filter(is_active=True)

        if query:
            queryset = queryset.filter(
                Q(business_name__icontains=query) |
                Q(business_description__icontains=query) |
                Q(city__icontains=query)
            )

        # Apply filters
        filters = {}
        category = request.GET.get('category')
        if category:
            queryset = queryset.filter(categories__slug=category)
            filters['category'] = category

        location = request.GET.get('location')
        if location:
            queryset = queryset.filter(city__icontains=location)
            filters['location'] = location

        # Limit and serialize
        providers = queryset.select_related('user').prefetch_related('categories')[:limit]

        from .serializers import ServiceProviderListSerializer
        serializer = ServiceProviderListSerializer(providers, many=True, context={'request': request})

        # Track search analytics for provider searches
        if query:
            self._track_search_analytics(
                query=query,
                filters=filters,
                results_count=len(serializer.data),
                response_time_ms=(time.time() - start_time) * 1000,
                request=request,
                search_type='providers'
            )

        return Response({
            'results': serializer.data,
            'count': len(serializer.data),
            'query': query,
            'type': 'providers'
        })

    def _extract_filters(self, request):
        """Extract search filters from request"""
        filters = {}

        # Category filter
        category = request.GET.get('category')
        if category:
            filters['category'] = category

        # Location filter
        location = request.GET.get('location')
        if location:
            filters['location'] = location

        # Price range
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        if min_price:
            filters['min_price'] = float(min_price)
        if max_price:
            filters['max_price'] = float(max_price)

        # Rating filter
        min_rating = request.GET.get('min_rating')
        if min_rating:
            filters['min_rating'] = float(min_rating)

        return filters

    def _track_search_analytics(self, query, filters, results_count, response_time_ms, request, search_type='services'):
        """Track search analytics asynchronously"""
        try:
            # Get client IP
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')

            # Create analytics record
            analytics_data = {
                'query': query,
                'filters': filters,
                'results_count': results_count,
                'response_time_ms': response_time_ms,
                'ip_address': ip_address,
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'session_id': request.session.session_key
            }

            # Add user if authenticated
            if request.user.is_authenticated:
                analytics_data['user'] = request.user

            # Add search type to filters for tracking
            analytics_data['filters']['search_type'] = search_type

            SearchAnalytics.objects.create(**analytics_data)

        except Exception as e:
            # Log error but don't fail the search request
            logging.error(f"Failed to track search analytics: {str(e)}")


class VoiceSearchView(APIView):
    """
    Voice search endpoint for processing audio input and returning search results
    """
    permission_classes = [AllowAny]
    parser_classes = [MultiPartParser, JSONParser]

    def post(self, request):
        """
        Process voice search request

        Expected input:
        - audio_file: Audio file (multipart/form-data)
        - language_code: Optional language code (default: en-US)
        """
        try:
            from .voice_search import VoiceSearchService

            # Get audio file from request
            audio_file = request.FILES.get('audio_file')
            if not audio_file:
                return Response({
                    'success': False,
                    'error': 'No audio file provided',
                    'code': 'missing_audio'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get language code
            language_code = request.data.get('language_code', 'en-US')

            # Read audio data
            audio_data = audio_file.read()

            # Validate audio data
            if len(audio_data) == 0:
                return Response({
                    'success': False,
                    'error': 'Empty audio file',
                    'code': 'empty_audio'
                }, status=status.HTTP_400_BAD_REQUEST)

            if len(audio_data) > 10 * 1024 * 1024:  # 10MB limit
                return Response({
                    'success': False,
                    'error': 'Audio file too large (max 10MB)',
                    'code': 'file_too_large'
                }, status=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE)

            # Process voice search
            voice_service = VoiceSearchService()
            result = voice_service.process_voice_search(audio_data, language_code)

            if not result['success']:
                return Response({
                    'success': False,
                    'error': result.get('error', 'Voice processing failed'),
                    'stage': result.get('stage', 'unknown'),
                    'code': 'processing_failed'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # If we have a valid search query, perform the search
            search_results = None
            if result['search_ready']:
                search_results = self._perform_search(result['query'])

            return Response({
                'success': True,
                'transcription': result['transcription'],
                'query': result['query'],
                'search_results': search_results,
                'search_ready': result['search_ready']
            })

        except Exception as e:
            logging.error(f"Voice search error: {str(e)}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'code': 'server_error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _perform_search(self, query_params):
        """Perform search based on processed voice query"""
        try:
            from .search_algorithms import AdvancedSearchAlgorithm

            search_algorithm = AdvancedSearchAlgorithm()

            # Build search filters from voice query
            filters = {}

            # Add category filter
            if query_params.get('categories'):
                # Map voice categories to actual category slugs
                category_mapping = {
                    'hair': 'hair-beauty',
                    'spa': 'spa-relaxation',
                    'massage': 'massage-therapy',
                    'facial': 'skincare-facial',
                    'nails': 'nail-services',
                    'makeup': 'makeup-beauty',
                    'waxing': 'hair-removal',
                    'barbershop': 'mens-grooming'
                }

                for category in query_params['categories']:
                    if category in category_mapping:
                        filters['category'] = category_mapping[category]
                        break

            # Add location filter
            if query_params.get('location'):
                filters['location'] = query_params['location']

            # Add other filters
            voice_filters = query_params.get('filters', {})
            if 'min_rating' in voice_filters:
                filters['min_rating'] = voice_filters['min_rating']

            if 'price_range' in voice_filters:
                if voice_filters['price_range'] == 'low':
                    filters['max_price'] = 50.0
                elif voice_filters['price_range'] == 'high':
                    filters['min_price'] = 100.0

            # Perform search
            search_query = query_params.get('query', '')
            if search_query:
                results = search_algorithm.search(search_query, filters=filters)
            else:
                # If no query, return popular services with filters
                from .models import Service
                queryset = Service.objects.filter(
                    is_active=True,
                    is_available=True,
                    is_popular=True
                ).select_related('provider', 'category')

                # Apply filters
                if 'category' in filters:
                    queryset = queryset.filter(category__slug=filters['category'])
                if 'location' in filters:
                    queryset = queryset.filter(provider__city__icontains=filters['location'])
                if 'min_rating' in filters:
                    queryset = queryset.filter(provider__rating__gte=filters['min_rating'])

                results = [
                    {
                        'service': service,
                        'relevance_score': 1.0,
                        'final_score': 1.0
                    }
                    for service in queryset[:10]
                ]

            # Serialize results
            services_data = []
            for result in results[:10]:  # Limit to 10 results
                service = result['service'] if isinstance(result, dict) else result
                from .serializers import ServiceListSerializer
                serializer = ServiceListSerializer(service, context={'request': self.request})
                service_data = serializer.data

                # Add search metadata
                if isinstance(result, dict):
                    service_data['search_metadata'] = {
                        'relevance_score': result.get('relevance_score', 1.0),
                        'final_score': result.get('final_score', 1.0),
                        'voice_search': True
                    }

                services_data.append(service_data)

            return {
                'services': services_data,
                'count': len(services_data),
                'filters_applied': filters,
                'intent': query_params.get('intent', 'search')
            }

        except Exception as e:
            logging.error(f"Voice search query execution error: {str(e)}")
            return {
                'services': [],
                'count': 0,
                'error': 'Search execution failed',
                'filters_applied': {}
            }


class SearchAnalyticsView(APIView):
    """
    API view for tracking search analytics
    """
    permission_classes = [AllowAny]

    def post(self, request):
        """Track a search query"""
        try:
            data = request.data

            # Validate required fields
            if not data.get('query'):
                return Response(
                    {'error': 'Query is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create analytics record
            analytics_data = {
                'query': data['query'],
                'filters': data.get('filters', {}),
                'results_count': data.get('results_count', 0),
                'response_time_ms': data.get('response_time_ms'),
                'session_id': data.get('session_id'),
                'ip_address': self.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')
            }

            # Add user if authenticated
            if request.user.is_authenticated:
                analytics_data['user'] = request.user

            analytics = SearchAnalytics.objects.create(**analytics_data)

            return Response(
                {'id': str(analytics.id), 'message': 'Analytics tracked successfully'},
                status=status.HTTP_201_CREATED
            )

        except Exception as e:
            logging.error(f"Search analytics tracking error: {str(e)}")
            return Response(
                {'error': 'Failed to track analytics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_client_ip(self, request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SearchPopularQueriesView(APIView):
    """
    API view for getting popular search queries
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """Get popular search queries"""
        try:
            limit = int(request.query_params.get('limit', 10))
            days = int(request.query_params.get('days', 30))

            # Limit the maximum number of queries returned
            limit = min(limit, 50)

            popular_queries = SearchAnalytics.get_popular_queries(limit=limit, days=days)

            # Format response
            queries_data = [
                {
                    'query': item['query'],
                    'count': item['search_count']
                }
                for item in popular_queries
            ]

            return Response({
                'queries': queries_data,
                'limit': limit,
                'days': days
            })

        except Exception as e:
            logging.error(f"Popular queries API error: {str(e)}")
            return Response(
                {'error': 'Failed to get popular queries'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SearchAnalyticsSummaryView(APIView):
    """
    API view for getting search analytics summary
    """
    permission_classes = [AllowAny]

    def get(self, request):
        """Get search analytics summary"""
        try:
            timeframe = request.query_params.get('timeframe', 'week')

            # Validate timeframe
            if timeframe not in ['day', 'week', 'month']:
                timeframe = 'week'

            summary = SearchAnalytics.get_analytics_summary(timeframe=timeframe)

            return Response(summary)

        except Exception as e:
            logging.error(f"Search analytics summary error: {str(e)}")
            return Response(
                {'error': 'Failed to get analytics summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
