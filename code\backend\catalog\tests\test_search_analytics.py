"""
Tests for search analytics functionality
"""

import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken


from catalog.models import ServiceCategory, Service, ServiceProvider, SearchAnalytics
from authentication.models import UserProfile

User = get_user_model()


class SearchAnalyticsModelTest(TestCase):
    """Test SearchAnalytics model functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = ServiceCategory.objects.create(
            name='Test Category',
            description='Test category description'
        )

    def test_search_analytics_creation(self):
        """Test creating a search analytics record"""
        analytics = SearchAnalytics.objects.create(
            query='test query',
            user=self.user,
            filters={'category': 'test'},
            results_count=5,
            response_time_ms=150.5
        )
        
        self.assertEqual(analytics.query, 'test query')
        self.assertEqual(analytics.user, self.user)
        self.assertEqual(analytics.filters['category'], 'test')
        self.assertEqual(analytics.results_count, 5)
        self.assertEqual(analytics.response_time_ms, 150.5)
        self.assertIsNotNone(analytics.timestamp)

    def test_search_analytics_str_representation(self):
        """Test string representation of SearchAnalytics"""
        analytics = SearchAnalytics.objects.create(
            query='test query',
            user=self.user,
            results_count=3
        )
        
        expected = f"Search: 'test query' by {self.user.email} (3 results)"
        self.assertEqual(str(analytics), expected)

    def test_search_analytics_without_user(self):
        """Test creating analytics record without user (anonymous search)"""
        analytics = SearchAnalytics.objects.create(
            query='anonymous search',
            results_count=2,
            session_id='test-session-123'
        )
        
        self.assertEqual(analytics.query, 'anonymous search')
        self.assertIsNone(analytics.user)
        self.assertEqual(analytics.session_id, 'test-session-123')


class SearchAnalyticsAPITest(APITestCase):
    """Test search analytics API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user_profile = UserProfile.objects.create(
            user=self.user,
            address='123 Test St',
            city='Test City',
            state='Test State',
            zip_code='12345',
            country='Test Country'
        )
        
        # Create JWT token for authentication
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        self.category = ServiceCategory.objects.create(
            name='Test Category',
            description='Test category description'
        )
        
        # Create some test search analytics data
        self.create_test_analytics_data()

    def create_test_analytics_data(self):
        """Create test search analytics data"""
        now = timezone.now()
        
        # Create analytics for the last week
        for i in range(7):
            date = now - timedelta(days=i)
            SearchAnalytics.objects.create(
                query=f'test query {i}',
                user=self.user if i % 2 == 0 else None,
                results_count=i + 1,
                response_time_ms=100 + i * 10,
                timestamp=date,
                filters={'category': 'test'} if i % 3 == 0 else {}
            )
        
        # Create popular queries
        popular_queries = ['hair salon', 'plumber', 'electrician', 'hair salon', 'plumber']
        for query in popular_queries:
            SearchAnalytics.objects.create(
                query=query,
                user=self.user,
                results_count=5,
                response_time_ms=120
            )

    def test_track_search_analytics_authenticated(self):
        """Test tracking search analytics for authenticated user"""
        url = reverse('catalog:search-analytics')
        data = {
            'query': 'test search',
            'filters': {'category': 'test'},
            'results_count': 10,
            'response_time_ms': 150.5
        }
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify analytics record was created
        analytics = SearchAnalytics.objects.filter(query='test search').first()
        self.assertIsNotNone(analytics)
        self.assertEqual(analytics.user, self.user)
        self.assertEqual(analytics.results_count, 10)

    def test_track_search_analytics_anonymous(self):
        """Test tracking search analytics for anonymous user"""
        url = reverse('catalog:search-analytics')
        data = {
            'query': 'anonymous search',
            'filters': {},
            'results_count': 5,
            'session_id': 'test-session-123'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify analytics record was created
        analytics = SearchAnalytics.objects.filter(query='anonymous search').first()
        self.assertIsNotNone(analytics)
        self.assertIsNone(analytics.user)
        self.assertEqual(analytics.session_id, 'test-session-123')

    def test_get_popular_queries(self):
        """Test getting popular search queries"""
        url = reverse('catalog:search-popular')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('queries', response.data)
        
        queries = response.data['queries']
        self.assertIsInstance(queries, list)
        
        # Check that popular queries are returned
        query_texts = [q['query'] for q in queries]
        self.assertIn('hair salon', query_texts)
        self.assertIn('plumber', query_texts)

    def test_get_popular_queries_with_limit(self):
        """Test getting popular queries with limit parameter"""
        url = reverse('catalog:search-popular')
        
        response = self.client.get(url, {'limit': 2})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        queries = response.data['queries']
        self.assertLessEqual(len(queries), 2)

    def test_get_search_analytics_summary(self):
        """Test getting search analytics summary"""
        url = reverse('catalog:search-analytics-summary')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        expected_fields = [
            'total_searches',
            'unique_queries',
            'popular_categories',
            'average_results_per_search',
            'success_rate',
            'average_response_time'
        ]
        
        for field in expected_fields:
            self.assertIn(field, response.data)

    def test_get_search_analytics_summary_with_timeframe(self):
        """Test getting search analytics summary with different timeframes"""
        url = reverse('catalog:search-analytics-summary')
        
        for timeframe in ['day', 'week', 'month']:
            response = self.client.get(url, {'timeframe': timeframe})
            
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertIn('total_searches', response.data)

    def test_invalid_search_analytics_data(self):
        """Test tracking search analytics with invalid data"""
        url = reverse('catalog:search-analytics')
        
        # Missing required query field
        data = {
            'filters': {},
            'results_count': 5
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_search_analytics_permissions(self):
        """Test that analytics endpoints don't require authentication for basic access"""
        # Popular queries should be accessible without authentication
        url = reverse('catalog:search-popular')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Analytics summary should be accessible without authentication
        url = reverse('catalog:search-analytics-summary')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)


class SearchAnalyticsIntegrationTest(APITestCase):
    """Integration tests for search analytics with search functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.category = ServiceCategory.objects.create(
            name='Test Category',
            description='Test category description'
        )
        
        # Create test service provider first
        self.provider = ServiceProvider.objects.create(
            user=self.user,
            business_name='Test Provider',
            business_description='Test provider description',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Provider St',
            city='Test City',
            state='Test State',
            zip_code='12345',
            country='Test Country'
        )

        # Create test service
        self.service = Service.objects.create(
            name='Test Service',
            description='Test service description',
            category=self.category,
            provider=self.provider,
            base_price=100.00,
            duration=60
        )

    def test_search_with_analytics_tracking(self):
        """Test that search operations trigger analytics tracking"""
        url = reverse('catalog:enhanced-search')

        response = self.client.get(url, {
            'search': 'test service',
            'category': self.category.id
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that the search endpoint is working
        # Analytics tracking integration would be tested separately
        self.assertTrue(True)  # Placeholder for actual integration test
