/**
 * Login Navigation Fix Verification Test
 * Tests the fix for LOGIN-NAV-001: Login navigation failure after successful authentication
 */

import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppNavigator } from '../../navigation/AppNavigator';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiSet: jest.fn(),
  multiGet: jest.fn(),
  multiRemove: jest.fn(),
  clear: jest.fn(),
}));

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  NavigationContainer: ({ children }: any) => children,
  useNavigation: () => ({
    navigate: jest.fn(),
    replace: jest.fn(),
    goBack: jest.fn(),
  }),
  useFocusEffect: jest.fn(),
}));

jest.mock('@react-navigation/stack', () => ({
  createStackNavigator: () => ({
    Navigator: ({ children }: any) => children,
    Screen: ({ children }: any) => children,
  }),
}));

// Mock React Native components
jest.mock('react-native', () => ({
  View: ({ children, testID }: any) => <div data-testid={testID}>{children}</div>,
  Text: ({ children, testID }: any) => <div data-testid={testID}>{children}</div>,
  Pressable: ({ children, testID, onPress }: any) => (
    <button data-testid={testID} onClick={onPress}>{children}</button>
  ),
  Linking: {
    openURL: jest.fn(),
  },
}));

// Mock the navigators
jest.mock('../../navigation/AuthNavigator', () => ({
  AuthNavigator: () => <div data-testid="auth-navigator">Auth Navigator</div>,
}));

jest.mock('../../navigation/MainNavigator', () => ({
  MainNavigator: () => <div data-testid="main-navigator">Main Navigator</div>,
}));

jest.mock('../../screens/LoadingScreen', () => ({
  LoadingScreen: () => <div data-testid="loading-screen">Loading...</div>,
}));

// Mock API client
jest.mock('../../services/api/client', () => ({
  apiClient: {
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  },
}));

describe('LOGIN-NAV-001: Login Navigation Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should show AuthNavigator when user is not authenticated', async () => {
    // Mock unauthenticated state
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === 'access_token') return Promise.resolve(null);
      if (key === 'user') return Promise.resolve(null);
      return Promise.resolve(null);
    });

    const { getByTestId, queryByTestId } = render(<AppNavigator />);

    await waitFor(() => {
      expect(queryByTestId('loading-screen')).toBeNull();
    });

    expect(getByTestId('auth-navigator')).toBeTruthy();
    expect(queryByTestId('main-navigator')).toBeNull();
  });

  it('should show MainNavigator when user is authenticated', async () => {
    // Mock authenticated state
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      role: 'customer',
      firstName: 'Test',
      lastName: 'User',
    };

    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === 'access_token') return Promise.resolve('mock-token');
      if (key === 'user') return Promise.resolve(JSON.stringify(mockUser));
      return Promise.resolve(null);
    });

    const { getByTestId, queryByTestId } = render(<AppNavigator />);

    await waitFor(() => {
      expect(queryByTestId('loading-screen')).toBeNull();
    });

    expect(getByTestId('main-navigator')).toBeTruthy();
    expect(queryByTestId('auth-navigator')).toBeNull();
  });

  it('should switch from Auth to Main navigator after login success', async () => {
    // Start with unauthenticated state
    let mockTokenValue: string | null = null;
    let mockUserValue: string | null = null;

    (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
      if (key === 'access_token') return Promise.resolve(mockTokenValue);
      if (key === 'user') return Promise.resolve(mockUserValue);
      return Promise.resolve(null);
    });

    const { getByTestId, queryByTestId, rerender } = render(<AppNavigator />);

    // Initially should show AuthNavigator
    await waitFor(() => {
      expect(queryByTestId('loading-screen')).toBeNull();
    });
    expect(getByTestId('auth-navigator')).toBeTruthy();

    // Simulate successful login by updating AsyncStorage values
    mockTokenValue = 'new-token';
    mockUserValue = JSON.stringify({
      id: 1,
      email: '<EMAIL>',
      role: 'customer',
    });

    // Rerender to trigger auth state change
    rerender(<AppNavigator />);

    // Should now show MainNavigator
    await waitFor(() => {
      expect(getByTestId('main-navigator')).toBeTruthy();
    });
    expect(queryByTestId('auth-navigator')).toBeNull();
  });
});
