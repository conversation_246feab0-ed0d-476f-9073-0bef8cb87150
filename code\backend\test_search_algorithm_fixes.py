"""
Test suite for EPIC-FIX-002: Search Algorithm and Indexing Issues

This test suite reproduces the exact failures found in the search algorithm tests
and verifies the fixes for:
1. SearchSuggestionEngine API mismatch (dict vs string returns)
2. Missing context parameter support
3. Caching mechanism issues
4. Voice search processing failures
5. Search algorithm false positives
6. Contextual synonym matching failures

Following TDD protocol - these tests should initially FAIL, then PASS after fixes.
"""

import time
from django.test import TestCase
from django.core.cache import cache
from unittest.mock import patch, MagicMock

from catalog.models import Service, ServiceProvider, ServiceCategory
from catalog.search_algorithms import (
    AdvancedSearchAlgorithm,
    SearchSuggestionEngine,
    VoiceSearchProcessor
)
from catalog.factories import ServiceFactory, ProviderFactory, ServiceCategoryFactory


class SearchAlgorithmFixTests(TestCase):
    """Test suite to reproduce and verify search algorithm fixes"""
    
    def setUp(self):
        """Set up test data"""
        # Create test categories
        self.beauty_category = ServiceCategoryFactory.create_category(
            name="Beauty & Hair",
            slug="beauty-hair"
        )
        self.spa_category = ServiceCategoryFactory.create_category(
            name="Spa & Wellness",
            slug="spa-wellness"
        )

        # Create test providers
        self.hair_provider = ProviderFactory.create_provider(
            category_slug="beauty-hair",
            business_name="Elite Hair Studio",
            city="New York"
        )
        self.spa_provider = ProviderFactory.create_provider(
            category_slug="spa-wellness",
            business_name="Zen Spa Retreat",
            city="Los Angeles"
        )

        # Create test services
        self.hair_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Professional Hair Styling",
            description="Expert hair cutting and styling services",
            is_popular=True
        )
        self.massage_service = ServiceFactory.create_service(
            provider=self.spa_provider,
            name="Relaxing Massage Therapy",
            description="Full body relaxation massage",
            is_popular=True
        )
        
        # Initialize search components
        self.search_algorithm = AdvancedSearchAlgorithm()
        self.suggestion_engine = SearchSuggestionEngine()
        self.voice_processor = VoiceSearchProcessor()


class TestSuggestionEngineAPIFix(SearchAlgorithmFixTests):
    """Test SearchSuggestionEngine API consistency fixes"""
    
    def test_suggestions_return_strings_not_dicts(self):
        """Test that get_suggestions returns List[str] when return_strings=True"""
        suggestions = self.suggestion_engine.get_suggestions("hai", return_strings=True)

        # Should return list of strings
        self.assertIsInstance(suggestions, list)
        if suggestions:  # If any suggestions returned
            self.assertIsInstance(suggestions[0], str,
                "Suggestions should be strings, not dicts")
    
    def test_suggestions_support_context_parameter(self):
        """Test that get_suggestions accepts context parameter"""
        try:
            # This should not raise TypeError
            suggestions = self.suggestion_engine.get_suggestions("cut", context="hair", return_strings=True)
            self.assertIsInstance(suggestions, list)
        except TypeError as e:
            self.fail(f"get_suggestions should accept context parameter: {e}")
    
    def test_suggestions_can_be_joined_as_strings(self):
        """Test that suggestions can be joined as strings (test expectation)"""
        suggestions = self.suggestion_engine.get_suggestions("hai", return_strings=True)

        if suggestions:
            try:
                # This should not raise TypeError
                suggestion_text = " ".join(suggestions).lower()
                self.assertIsInstance(suggestion_text, str)
            except TypeError as e:
                self.fail(f"Suggestions should be joinable as strings: {e}")

    def test_suggestions_support_startswith_method(self):
        """Test that suggestions support string methods like startswith"""
        suggestions = self.suggestion_engine.get_suggestions("h", return_strings=True)

        if suggestions:
            try:
                # This should not raise AttributeError
                result = suggestions[0].startswith("h")
                self.assertIsInstance(result, bool)
            except AttributeError as e:
                self.fail(f"Suggestions should support string methods: {e}")


class TestSuggestionCachingFix(SearchAlgorithmFixTests):
    """Test suggestion caching mechanism fixes"""
    
    def test_suggestion_cache_is_dict_like(self):
        """Test that suggestion engine cache behaves like a dict"""
        # Clear cache first
        cache.clear()
        
        # Generate suggestions to populate cache
        suggestions = self.suggestion_engine.get_suggestions("massage", return_strings=True)
        
        # Cache should be accessible as dict-like object
        cache_key = "massage_5_None_True_None_None"  # Updated format includes all parameters

        # This should work without AttributeError
        try:
            # Check if cache has dict-like interface
            self.assertTrue(hasattr(self.suggestion_engine.cache, '__contains__'))
            # The cache key should be findable
            cached_value = cache.get(cache_key)
            self.assertIsNotNone(cached_value, "Cache should contain the suggestions")
        except Exception as e:
            self.fail(f"Cache should behave like a dict: {e}")


class TestSearchAlgorithmFalsePositiveFix(SearchAlgorithmFixTests):
    """Test search algorithm false positive fixes"""
    
    def test_no_match_returns_truly_empty_results(self):
        """Test that completely unrelated queries return empty results"""
        # Query that should have absolutely no matches
        results = self.search_algorithm.search("nonexistent service xyz quantum physics")
        
        # Should return empty list, not 2 results
        self.assertEqual(len(results), 0, 
            "Completely unrelated queries should return empty results")
    
    def test_contextual_synonym_matching_works(self):
        """Test that contextual synonym matching works correctly"""
        # Search for "beautician" should find hair services
        results = self.search_algorithm.search("beautician")
        
        # Should find hair-related services
        found_hair_service = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_hair_service, 
            "Contextual synonym 'beautician' should find hair services")


class TestVoiceSearchProcessingFix(SearchAlgorithmFixTests):
    """Test voice search processing fixes"""
    
    def test_voice_query_normalization_accuracy(self):
        """Test that voice queries are properly normalized"""
        # Test voice input that should normalize to "hair cut"
        voice_input = "I need a ha rcut"  # Simulated speech recognition error
        
        normalized = self.voice_processor.normalize_voice_query(voice_input)
        
        # Should properly normalize to contain "hair"
        self.assertIn("hair", normalized.lower(), 
            "Voice normalization should fix speech recognition errors")
    
    def test_voice_error_handling_for_unclear_input(self):
        """Test that unclear voice input is handled with error status"""
        # Simulate unclear voice input
        unclear_input = "umm... uh... something..."
        
        result = self.voice_processor.process_voice_query(unclear_input)
        
        # Should return error status, not success
        self.assertIn("error", result.get("status", "").lower(),
            "Unclear voice input should return error status")
    
    def test_voice_search_accuracy_improvement(self):
        """Test that voice search produces accurate results"""
        # Voice query for hair services
        voice_query = "I want hair styling services"
        
        result = self.voice_processor.process_voice_query(voice_query)
        
        # Should find relevant services
        self.assertEqual(result.get("status"), "success")
        self.assertGreater(len(result.get("results", [])), 0)
        
        # Should find hair-related services
        found_relevant = any(
            "hair" in str(service).lower() or "styling" in str(service).lower()
            for service in result.get("results", [])
        )
        self.assertTrue(found_relevant, 
            "Voice search should find relevant hair styling services")


class TestSearchIndexOptimizationFix(SearchAlgorithmFixTests):
    """Test search index optimization fixes"""
    
    def test_search_index_handles_service_deletion(self):
        """Test that search index properly handles service deletion"""
        # Create a service and index it
        temp_service = ServiceFactory.create_service(
            provider=self.hair_provider,
            name="Temporary Service"
        )
        
        # Search should find it
        results = self.search_algorithm.search("Temporary Service")
        self.assertGreater(len(results), 0)
        
        # Delete the service
        temp_service.delete()
        
        # Search should no longer find it
        results = self.search_algorithm.search("Temporary Service")
        self.assertEqual(len(results), 0, 
            "Deleted services should not appear in search results")
    
    def test_search_index_handles_service_modification(self):
        """Test that search index properly handles service modifications"""
        # Modify existing service
        original_name = self.hair_service.name
        self.hair_service.name = "Updated Hair Styling Service"
        self.hair_service.save()
        
        # Search with new name should find it
        results = self.search_algorithm.search("Updated Hair Styling")
        found_updated = any(
            result['service'].id == self.hair_service.id
            for result in results
        )
        self.assertTrue(found_updated, 
            "Modified services should be findable with new name")
        
        # Search with old name should not find it (or find it with lower relevance)
        results = self.search_algorithm.search(original_name)
        if results:
            # If found, should have lower relevance than exact match
            found_with_old_name = any(
                result['service'].id == self.hair_service.id and result['relevance_score'] < 0.9
                for result in results
            )
            # Either not found or found with lower relevance is acceptable
