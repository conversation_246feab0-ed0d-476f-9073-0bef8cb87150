"""
URL configuration for vierla_project project.
Vierla Beauty Services Marketplace Backend
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)


def api_docs_view(request):
    """Simple API documentation endpoint"""
    docs = {
        "title": "Vierla Beauty Services API",
        "version": "2.0.0",
        "description": "Comprehensive API for beauty services marketplace with mobile optimizations",
        "endpoints": {
            "authentication": {
                "base_url": "/api/auth/",
                "endpoints": [
                    "POST /api/auth/login/ - User login",
                    "POST /api/auth/register/ - User registration",
                    "POST /api/auth/logout/ - User logout",
                    "POST /api/auth/token/refresh/ - Refresh JWT token",
                    "GET /api/auth/profile/ - Get user profile",
                    "PUT /api/auth/profile/details/ - Update user profile",
                ]
            },
            "catalog": {
                "base_url": "/api/catalog/",
                "endpoints": [
                    "GET /api/catalog/categories/ - List service categories",
                    "GET /api/catalog/services/ - List services",
                    "GET /api/catalog/providers/ - List service providers",
                    "GET /api/catalog/search/ - Search services and providers"
                ]
            }
        },
        "authentication": {
            "type": "JWT Bearer Token",
            "header": "Authorization: Bearer <token>",
            "obtain_token": "POST /api/auth/login/ with email and password"
        }
    }
    return JsonResponse(docs, json_dumps_params={'indent': 2})

urlpatterns = [
    # Admin interface
    path("admin/", admin.site.urls),

    # API Documentation
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='api-docs'),
    path('api/docs/json/', api_docs_view, name='api-docs-json'),
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/schema/swagger-ui/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # New versioned, role-based API structure
    path('api/v1/', include('api.v1.urls')),

    # Legacy API endpoints (maintained for backward compatibility)
    path('api/auth/', include('authentication.urls')),
    path('api/catalog/', include('catalog.urls')),
    path('api/provider/', include('catalog.provider_urls')),
    path('api/bookings/', include('bookings.urls')),
    path('api/payments/', include('payments.urls')),
    path('api/messaging/', include('messaging.urls')),
    path('api/reviews/', include('reviews.urls')),
    path('api/notifications/', include('notifications.urls')),
    path('api/analytics/', include('analytics.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
