"""
V<PERSON><PERSON>end - Base Settings
Common settings for all environments
"""

from pathlib import Path
from datetime import timedelta
import os
import environ
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Environment variables with django-environ for better handling
env = environ.Env(
    DEBUG=(bool, False),
    USE_SQLITE=(bool, False),
    DB_PORT=(int, 5432),
)

# Load environment variables from .env file (with precedence for system env vars)
load_dotenv(BASE_DIR / '.env')
environ.Env.read_env(BASE_DIR / '.env')

# Environment configuration
ENVIRONMENT = os.environ.get('DJANGO_ENVIRONMENT', 'development')

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY', default="django-insecure-_%9ujahqx8tqffc7qisew580ht6id0va+h-_x!%61s!!k10*_$")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG', default=True)

# Application definition
DJANGO_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'drf_spectacular',  # API documentation
]

LOCAL_APPS = [
    'api',  # New versioned API structure
    'authentication',
    'catalog',
    'bookings',
    'reviews',
    'payments',
    'messaging',
    'notifications',
    'analytics',
    'services',
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    "django.middleware.security.SecurityMiddleware",
    # Advanced Rate Limiting and DDoS Protection (EPIC-AUDIT-002) - TEMPORARILY DISABLED FOR DEVELOPMENT
    # 'vierla_project.middleware.advanced_rate_limiting.AdvancedRateLimitingMiddleware',
    # 'vierla_project.middleware.advanced_rate_limiting.AuthenticationRateLimitingMiddleware',
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "vierla_project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "vierla_project.wsgi.application"

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Database - Optimized PostgreSQL Configuration for Production Performance
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',  # Regular PostgreSQL for now
        'NAME': env('DB_NAME', default='vierla_db'),
        'USER': env('DB_USER', default='vierla_user'),
        'PASSWORD': env('DB_PASSWORD', default='vierla_password'),
        'HOST': env('DB_HOST', default='localhost'),
        'PORT': env('DB_PORT', default=5432),
        'OPTIONS': {
            'sslmode': env('DB_SSLMODE', default='prefer'),
            # Performance optimization options
            'connect_timeout': 10,
            'options': '-c default_transaction_isolation=read_committed'
        },
        # Connection pooling and performance settings
        'CONN_MAX_AGE': 600,  # 10 minutes
        'CONN_HEALTH_CHECKS': True,
        'ATOMIC_REQUESTS': False,  # Disabled for better performance
    }
}

# Internationalization
LANGUAGE_CODE = "en-us"
TIME_ZONE = "UTC"
USE_I18N = True
USE_TZ = True

# Voice Search Configuration
SPEECH_TO_TEXT_PROVIDER = os.getenv('SPEECH_TO_TEXT_PROVIDER', 'mock')  # 'google', 'azure', 'mock'
SPEECH_CACHE_TIMEOUT = int(os.getenv('SPEECH_CACHE_TIMEOUT', '3600'))  # 1 hour

# Google Cloud Speech API settings
GOOGLE_CLOUD_PROJECT = os.getenv('GOOGLE_CLOUD_PROJECT', '')
GOOGLE_APPLICATION_CREDENTIALS = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')

# Azure Speech API settings
AZURE_SPEECH_KEY = os.getenv('AZURE_SPEECH_KEY', '')
AZURE_SPEECH_REGION = os.getenv('AZURE_SPEECH_REGION', 'eastus')

# Static files (CSS, JavaScript, Images)
STATIC_URL = "static/"
STATIC_ROOT = BASE_DIR / "staticfiles"

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom User Model
AUTH_USER_MODEL = 'authentication.User'

# Django REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour',
        'mobile': '500/hour',
        'login': '10/minute',
        'register': '5/minute',
        'password_reset': '3/hour',
    },
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# JWT Configuration with RS256 Algorithm (Production Security)
# Import JWT key utilities
try:
    from vierla_project.utils.jwt_keys import get_jwt_private_key, get_jwt_public_key, get_development_fallback_key

    # Try to load RSA keys for RS256 algorithm
    try:
        JWT_PRIVATE_KEY = get_jwt_private_key()
        JWT_PUBLIC_KEY = get_jwt_public_key()
        JWT_ALGORITHM = 'RS256'
        JWT_SIGNING_KEY = JWT_PRIVATE_KEY
        JWT_VERIFYING_KEY = JWT_PUBLIC_KEY
        print("✅ JWT configured with RS256 algorithm using RSA keys")
    except Exception as e:
        # Fallback to HS256 for development if RSA keys are not available
        JWT_ALGORITHM = 'HS256'
        JWT_SIGNING_KEY = get_development_fallback_key()
        JWT_VERIFYING_KEY = None
        print(f"⚠️ JWT fallback to HS256: {e}")

except ImportError:
    # Fallback if utils module is not available
    JWT_ALGORITHM = 'HS256'
    JWT_SIGNING_KEY = SECRET_KEY
    JWT_VERIFYING_KEY = None
    print("⚠️ JWT utils not available, using HS256 fallback")

# Redis Configuration for Rate Limiting (EPIC-AUDIT-002)
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', 'localhost'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'db': int(os.getenv('REDIS_DB', 1)),
    'decode_responses': True,
    'socket_connect_timeout': 5,
    'socket_timeout': 5,
    'retry_on_timeout': True,
    'health_check_interval': 30,
}

# Advanced Rate Limiting Configuration (EPIC-AUDIT-002)
ADVANCED_RATE_LIMITING = {
    'ddos_protection': True,
    'auto_block': True,
    'default_limits': {
        'anonymous': {'limit': 100, 'window': 3600, 'burst': 20},  # 100/hour, 20/minute burst
        'authenticated': {'limit': 1000, 'window': 3600, 'burst': 50},  # 1000/hour, 50/minute burst
        'premium': {'limit': 5000, 'window': 3600, 'burst': 100},  # 5000/hour, 100/minute burst
    },
    'endpoint_limits': {
        'auth_login': {'limit': 10, 'window': 600, 'burst': 5},  # 10/10min, 5/minute burst
        'auth_register': {'limit': 5, 'window': 3600, 'burst': 2},  # 5/hour, 2/minute burst
        'password_reset': {'limit': 3, 'window': 3600, 'burst': 1},  # 3/hour, 1/minute burst
        'search': {'limit': 200, 'window': 3600, 'burst': 30},  # 200/hour, 30/minute burst
        'upload': {'limit': 50, 'window': 3600, 'burst': 10},  # 50/hour, 10/minute burst
    },
    'ddos_thresholds': {
        'requests_per_second': 50,
        'requests_per_minute': 1000,
        'failed_requests_ratio': 0.8,
        'unique_endpoints_threshold': 20,
        'suspicious_user_agents': ['bot', 'crawler', 'spider', 'scraper', 'scanner'],
    },
    'block_durations': {
        'low_threat': 300,      # 5 minutes
        'medium_threat': 1800,  # 30 minutes
        'high_threat': 3600,    # 1 hour
        'critical_threat': 7200, # 2 hours
    }
}

SIMPLE_JWT = {
    # Token Lifetimes (Production-ready shorter lifetimes)
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),  # Shorter for better security
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),     # Shorter for better security

    # Token Rotation and Security
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,

    # Algorithm and Keys (RS256 for production security)
    'ALGORITHM': JWT_ALGORITHM,
    'SIGNING_KEY': JWT_SIGNING_KEY,
    'VERIFYING_KEY': JWT_VERIFYING_KEY,

    # Token Claims and Validation
    'AUDIENCE': None,
    'ISSUER': 'vierla-backend',
    'JWK_URL': None,
    'LEEWAY': 10,  # 10 seconds leeway for clock skew

    # Authentication Headers
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',

    # User Integration
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'USER_AUTHENTICATION_RULE': 'rest_framework_simplejwt.authentication.default_user_authentication_rule',

    # Token Classes and Claims
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'TOKEN_USER_CLASS': 'rest_framework_simplejwt.models.TokenUser',
    'JTI_CLAIM': 'jti',

    # Sliding Token Configuration (not used but kept for compatibility)
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=5),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}

# API Documentation Configuration
SPECTACULAR_SETTINGS = {
    'TITLE': 'Vierla Beauty Services API',
    'DESCRIPTION': 'Comprehensive API for beauty services marketplace with mobile optimizations',
    'VERSION': '2.0.0',
    'SERVE_INCLUDE_SCHEMA': True,
    'SCHEMA_PATH_PREFIX': '/api/v[0-9]',
    'COMPONENT_SPLIT_REQUEST': True,
    'SORT_OPERATIONS': False,
    'DISABLE_ERRORS_AND_WARNINGS': True,
    'SCHEMA_PATH_PREFIX_TRIM': True,
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': True,
    },
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8081",
    "http://127.0.0.1:8081",
    "http://************:8081",
    "http://********:8081",
]

CORS_ALLOW_CREDENTIALS = True

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
