"""
URL configuration for catalog app
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ServiceCategoryViewSet, ServiceProviderViewSet, ServiceViewSet,
    EnhancedSearchView, VoiceSearchView, SearchAnalyticsView,
    SearchPopularQueriesView, SearchAnalyticsSummaryView
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'categories', ServiceCategoryViewSet, basename='servicecategory')
router.register(r'providers', ServiceProviderViewSet, basename='serviceprovider')
router.register(r'services', ServiceViewSet, basename='service')

app_name = 'catalog'

urlpatterns = [
    path('', include(router.urls)),

    # Enhanced search endpoints
    path('search/enhanced/', EnhancedSearchView.as_view(), name='enhanced-search'),

    # Voice search endpoints
    path('search/voice/', VoiceSearchView.as_view(), name='voice-search'),

    # Search analytics endpoints
    path('search/analytics/', SearchAnalyticsView.as_view(), name='search-analytics'),
    path('search/popular/', SearchPopularQueriesView.as_view(), name='search-popular'),
    path('search/analytics/summary/', SearchAnalyticsSummaryView.as_view(), name='search-analytics-summary'),
]
